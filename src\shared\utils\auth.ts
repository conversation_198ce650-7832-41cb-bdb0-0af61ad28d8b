import { Logger } from './Logger'

const logger = Logger.getContextLogger('AUTH')

export const extractAuth = (
  authString: string
): { session: string; uid: number; isDemo?: number } | null => {
  try {
    const json = authString.substring(2)
    const auth = JSON.parse(json)

    if (Array.isArray(auth) && auth.length > 1 && typeof auth[1] === 'object') {
      const data = auth[1]
      const { session, uid, isDemo } = data

      if (session && uid) {
        if (isDemo) {
          return { session, uid, isDemo }
        }

        return { session, uid, isDemo: 0 }
      }
    }

    return null
  } catch (error) {
    const message = error instanceof Error ? error.message : 'Unknown error'
    logger.error(`Failed to extract auth data: ${message}`)
    return null
  }
}
