import MoneyManager from '../main/core/MoneyManager'

describe('Money Management System', () => {
  let moneyManager: MoneyManager

  beforeEach(() => {
    moneyManager = new MoneyManager(1000) // $1000 initial balance
  })

  describe('Session Validation', () => {
    test('should validate trade capital correctly', () => {
      const validResult = moneyManager.validateTradeCapital(500)
      expect(validResult.isValid).toBe(true)

      const invalidResult = moneyManager.validateTradeCapital(1500)
      expect(invalidResult.isValid).toBe(false)
      expect(invalidResult.error).toContain('Insufficient account balance')

      const zeroResult = moneyManager.validateTradeCapital(0)
      expect(zeroResult.isValid).toBe(false)
      expect(zeroResult.error).toContain('must be greater than 0')
    })

    test('should start session with valid parameters', () => {
      expect(() => {
        moneyManager.startSession(500, 100, 50, 'martingale')
      }).not.toThrow()

      expect(moneyManager.isSessionActive()).toBe(true)

      const status = moneyManager.getSessionStatus()
      expect(status).not.toBeNull()
      expect(status!.initialCapital).toBe(500)
      expect(status!.targetProfit).toBe(100)
      expect(status!.nextTradeAmount).toBe(50)
    })

    test('should reject invalid session parameters', () => {
      expect(() => {
        moneyManager.startSession(-100, 50, 25, 'flat')
      }).toThrow('All parameters must be positive numbers')

      expect(() => {
        moneyManager.startSession(500, 100, 600, 'flat')
      }).toThrow('Initial trade amount cannot exceed trade capital')

      expect(() => {
        moneyManager.startSession(1500, 100, 50, 'flat')
      }).toThrow('Insufficient account balance')
    })
  })

  describe('Capital Protection', () => {
    beforeEach(() => {
      moneyManager.startSession(500, 100, 50, 'martingale')
    })

    test('should stop when target profit is reached', () => {
      // Simulate enough wins to reach target
      // Each win: 50 * 0.85 = 42.5 profit
      // After 2 wins: 85 profit (below target of 100)
      // After 3 wins: 127.5 profit (exceeds target of 100)

      moneyManager.placeTrade(true) // Win 1: 42.5 profit
      moneyManager.placeTrade(true) // Win 2: 85 profit total

      // Session should still be active
      expect(moneyManager.isSessionActive()).toBe(true)

      moneyManager.placeTrade(true) // Win 3: 127.5 profit total - should end session

      // Session should now be ended
      expect(moneyManager.isSessionActive()).toBe(false)
    })

    test('should stop when capital depleted to 10%', () => {
      // Simulate losses until capital is depleted
      const session = moneyManager.getCurrentSession()!

      // Force capital to 10% threshold
      session.remainingCapital = session.initialCapital * 0.05 // Below 10%

      const stopCheck = moneyManager.shouldStopSession()
      expect(stopCheck.shouldStop).toBe(true)
      expect(stopCheck.reason).toContain('Capital depleted')
    })

    test('should stop when next trade exceeds remaining capital', () => {
      const session = moneyManager.getCurrentSession()!

      // Force next trade amount to exceed remaining capital
      session.currentTradeAmount = session.remainingCapital + 100

      const stopCheck = moneyManager.shouldStopSession()
      expect(stopCheck.shouldStop).toBe(true)
      expect(stopCheck.reason).toContain('Insufficient capital for next trade')
    })
  })

  describe('Loss Recovery Strategies', () => {
    test('should implement flat strategy correctly', () => {
      moneyManager.startSession(500, 100, 50, 'flat')

      moneyManager.placeTrade(false) // Loss
      const status1 = moneyManager.getSessionStatus()!
      expect(status1.nextTradeAmount).toBe(50) // Same amount

      moneyManager.placeTrade(false) // Another loss
      const status2 = moneyManager.getSessionStatus()!
      expect(status2.nextTradeAmount).toBe(50) // Still same amount
    })

    test('should implement martingale strategy correctly', () => {
      moneyManager.startSession(500, 100, 50, 'martingale')

      moneyManager.placeTrade(false) // Loss
      const status1 = moneyManager.getSessionStatus()!
      expect(status1.nextTradeAmount).toBe(100) // Doubled

      moneyManager.placeTrade(false) // Another loss
      const status2 = moneyManager.getSessionStatus()!
      expect(status2.nextTradeAmount).toBe(200) // Doubled again

      // Check that session is still active before the win
      expect(moneyManager.isSessionActive()).toBe(true)

      moneyManager.placeTrade(true) // Win: 200 * 0.85 = 170 profit (exceeds target of 100)

      // Session should end because profit exceeds target
      expect(moneyManager.isSessionActive()).toBe(false)
    })

    test('should implement fibonacci strategy correctly', () => {
      moneyManager.startSession(500, 100, 50, 'fibonacci')

      moneyManager.placeTrade(false) // Loss
      const status1 = moneyManager.getSessionStatus()!
      expect(status1.nextTradeAmount).toBe(50) // 50 * 1 (fib[1])

      moneyManager.placeTrade(false) // Another loss
      const status2 = moneyManager.getSessionStatus()!
      expect(status2.nextTradeAmount).toBe(100) // 50 * 2 (fib[2])

      moneyManager.placeTrade(false) // Another loss
      const status3 = moneyManager.getSessionStatus()!
      expect(status3.nextTradeAmount).toBe(150) // 50 * 3 (fib[3])

      // Check that session is still active before the win
      expect(moneyManager.isSessionActive()).toBe(true)

      moneyManager.placeTrade(true) // Win: 150 * 0.85 = 127.5 profit (exceeds target of 100)

      // Session should end because profit exceeds target
      expect(moneyManager.isSessionActive()).toBe(false)
    })
  })

  describe('Session Results', () => {
    test('should generate correct session results', () => {
      moneyManager.startSession(500, 100, 50, 'martingale')

      // Simulate some trades that won't exceed target
      moneyManager.placeTrade(true) // Win: 42.5 profit
      moneyManager.placeTrade(false) // Loss: 0 additional profit

      // Get result before session ends
      const result = moneyManager.getSessionResult()!
      expect(result.totalTrades).toBe(2)
      expect(result.winCount).toBe(1)
      expect(result.lossCount).toBe(1)
      expect(result.successRate).toBe(50)
      expect(result.strategy).toBe('martingale')
      expect(result.profitable).toBe(true) // Should be profitable with 42.5 profit
    })

    test('should track session statistics correctly', () => {
      moneyManager.startSession(500, 100, 50, 'flat')

      const initialStatus = moneyManager.getSessionStatus()!
      expect(initialStatus.totalTrades).toBe(0)
      expect(initialStatus.winCount).toBe(0)
      expect(initialStatus.lossCount).toBe(0)
      expect(initialStatus.successRate).toBe(0)

      moneyManager.placeTrade(true)

      const afterWinStatus = moneyManager.getSessionStatus()!
      expect(afterWinStatus.totalTrades).toBe(1)
      expect(afterWinStatus.winCount).toBe(1)
      expect(afterWinStatus.successRate).toBe(100)
    })
  })

  describe('Real-time Status Updates', () => {
    test('should provide accurate real-time status', () => {
      moneyManager.startSession(500, 100, 50, 'martingale')

      const initialStatus = moneyManager.getSessionStatus()!
      expect(initialStatus.isSessionActive).toBe(true)
      expect(initialStatus.currentCapital).toBe(500)
      expect(initialStatus.currentProfit).toBe(0)

      moneyManager.placeTrade(true) // Win: +42.5 profit (50 * 0.85)

      const afterWinStatus = moneyManager.getSessionStatus()!
      expect(afterWinStatus.currentProfit).toBe(42.5)
      expect(afterWinStatus.currentCapital).toBe(542.5) // 500 + 42.5
    })

    test('should use actual profit percentages when provided', () => {
      moneyManager.startSession(500, 100, 50, 'martingale')

      // Test with actual profit percentage (different from 85% calculation)
      const actualProfitPercent = 77.5 // 77.5% profit (like from broker)
      moneyManager.placeTrade(true, actualProfitPercent)

      const status = moneyManager.getSessionStatus()!
      expect(status.currentProfit).toBe(38.75) // 50 * 0.775 = 38.75
      expect(status.currentCapital).toBe(538.75) // 500 + 38.75

      // Test with loss (no profit percentage needed for losses)
      moneyManager.placeTrade(false)

      const statusAfterLoss = moneyManager.getSessionStatus()!
      expect(statusAfterLoss.currentProfit).toBe(38.75) // Profit unchanged (loss doesn't affect currentProfit)
      // Capital calculation: 538.75 (previous) - 50 (trade amount used for this loss) = 488.75
      // Note: Trade amount is doubled AFTER the loss, so this loss used 50, not 100
      expect(statusAfterLoss.currentCapital).toBe(488.75)
      // The next trade amount should now be doubled
      expect(statusAfterLoss.nextTradeAmount).toBe(100)
    })
  })
})
