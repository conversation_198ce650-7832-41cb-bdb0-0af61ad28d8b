import { Logger } from '../../shared/utils/Logger'

import { TradingStrategy } from './TradingStrategy'
// import { Candle, TradingDecision, StrategyConfig } from '../types'
import { ATRStateful } from '../inicators/ATRStateful'

const logger = Logger.getContextLogger('BACK2TREND')

/**
 * Configuration for Back2Trend Strategy
 */
export interface Back2TrendConfig extends StrategyConfig {
  /** Number of candles to use for slope and consolidation detection */
  lookbackPeriod: number
  /** ATR multiplier for defining consolidation range */
  atrMultiplier: number
  /** Minimum slope (absolute) to consider a valid global trend */
  slopeThreshold: number
}

/**
 * Back2TrendStrategy:
 * - Detects a strong global trend via slope over lookbackPeriod
 * - Waits for price to pull back and consolidate (range < ATR * atrMultiplier)
 * - Enters on breakout of that consolidation in the trend direction
 * - Uses time-based expiry from config (expirySeconds or candlePeriodSeconds)
 */
export class Back2TrendStrategy extends TradingStrategy {
  private atrCalculator: ATRStateful
  private lookbackPeriod: number
  private atrMultiplier: number
  private slopeThreshold: number

  constructor(config: StrategyConfig) {
    super(config)

    const defaultConfig = {
      lookbackPeriod: 20, // Candles for trend/consolidation analysis
      atrMultiplier: 1.8, // ATR multiplier for consolidation range
      slopeThreshold: 0.0025, // Minimum trend strength (absolute per candle)
      atrPeriod: 14
    }
    this.lookbackPeriod = config.back2TrendConfig?.lookbackPeriod ?? defaultConfig.lookbackPeriod
    this.atrMultiplier = config.back2TrendConfig?.atrMultiplier ?? defaultConfig.atrMultiplier
    this.slopeThreshold = config.back2TrendConfig?.slopeThreshold ?? defaultConfig.slopeThreshold
    // ATR period can be configured via config.atrPeriod, default to 14
    this.atrCalculator = new ATRStateful(config.atrPeriod ?? defaultConfig.atrPeriod)
  }

  getName(): string {
    return 'Back 2 Trend Strategy'
  }

  getDescription(): string {
    return 'Enters on a pullback against a strong trend after consolidation and breakout of the trendline.'
  }

  getCandleCount(): number {
    // Need enough history for both slope and consolidation checks
    return this.lookbackPeriod + 1
  }

  async evaluate(candle: Candle): Promise<TradingDecision> {
    // Maintain history
    this.addCandle(candle)

    // Require sufficient data
    if (this.priceHistory.length <= this.lookbackPeriod) {
      return { shouldTrade: false, reason: 'Insufficient data', confidence: 0 }
    }

    // 1) Compute global trend slope
    const history = this.priceHistory
    const pastClose = history[history.length - 1 - this.lookbackPeriod].close
    const currentClose = history[history.length - 1].close
    const slope = (currentClose - pastClose) / this.lookbackPeriod

    // Reject if trend too weak
    if (Math.abs(slope) < this.slopeThreshold) {
      return { shouldTrade: false, reason: 'Trend not strong enough', confidence: 0 }
    }
    const trendDir = slope > 0 ? 'up' : 'down'

    // 2) Update ATR
    const prevClose = history[history.length - 2].close
    const atr = this.atrCalculator.update(candle, prevClose)
    if (atr === null) {
      return { shouldTrade: false, reason: 'Initializing ATR', confidence: 0 }
    }

    // 3) Detect consolidation: range within lookbackPeriod bars
    const window = history.slice(history.length - this.lookbackPeriod)
    const highs = window.map((c) => c.high)
    const lows = window.map((c) => c.low)
    const maxHigh = Math.max(...highs)
    const minLow = Math.min(...lows)

    if (maxHigh - minLow > atr * this.atrMultiplier) {
      return { shouldTrade: false, reason: 'No consolidation', confidence: 0 }
    }

    // 4) Entry on breakout of consolidation in trend direction
    const breakoutUp = candle.close > maxHigh
    const breakoutDown = candle.close < minLow

    if ((trendDir === 'up' && breakoutUp) || (trendDir === 'down' && breakoutDown)) {
      const breakoutDistance = trendDir === 'up' ? candle.close - maxHigh : minLow - candle.close

      const normalizedSlope = Math.abs(slope) / this.slopeThreshold
      const volatilityFactor = 1 - Math.min(atr / candle.close, 0.5) // Penalize high volatility
      const breakoutFactor = Math.min(breakoutDistance / (atr * 0.25), 1) // breakoutDistance / (0.001 + atr)	// Reward larger breakouts

      // const confidence = Math.min(Math.abs(slope) / this.slopeThreshold / 10, 1)
      const confidence = Math.min(
        normalizedSlope * 0.6 + breakoutFactor * 0.3 + volatilityFactor * 0.4 + 1
      )

      const direction = trendDir === 'up' ? 'high' : 'low'
      const expiry =
        (this.config as Back2TrendConfig).expirySeconds ?? this.config.candlePeriodSeconds

      logger.debug(
        `Entry: direction=${direction}, slope=${slope.toFixed(5)}, atr=${atr.toFixed(5)}`
      )

      return {
        shouldTrade: true,
        direction,
        confidence,
        reason: 'Breakout after consolidation',
        expirySeconds: expiry
      }
    }

    return { shouldTrade: false, reason: 'Awaiting breakout', confidence: 0 }
  }
}
