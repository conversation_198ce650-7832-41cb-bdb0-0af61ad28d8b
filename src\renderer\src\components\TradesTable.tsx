import { useEffect, useState, useRef } from 'react'
import { getCooldownSeconds } from '../../../shared/utils/formatters'

interface TradeData {
  id: string
  status: 'WIN' | 'LOSS' | 'PROGRESS'
  closeInSeconds: number
  direction: 'BUY' | 'SELL'
  payout: number
  amount: number
  profit: number
}

const TradesTable: React.FC = () => {
  const [balance, setBalance] = useState<number>(0)
  const [trades, setTrades] = useState<TradeData[]>([])
  const tradesContainerRef = useRef<HTMLDivElement>(null)

  const calculateWinLossRatio = (): string => {
    const finishedTrades = trades.filter((t) => t.status !== 'PROGRESS')
    const wins = finishedTrades.filter((t) => t.status === 'WIN').length
    const total = finishedTrades.length
    return total > 0 ? `${Math.round((wins / total) * 100)}%` : '0%'
  }

  useEffect(() => {
    const unsub = window.api.on('broker:event', (...args: unknown[]) => {
      const [event, data] = args
      if (event === 'balance:updated') {
        const balanceData = data as Balance
        setBalance(balanceData.balance)
      }

      if (event === 'trade:opened') {
        const tradeData = data as OpenOrderResponse
        setTrades((prevTrades) => [
          ...prevTrades,
          {
            id: tradeData.id,
            status: 'PROGRESS',
            closeInSeconds: getCooldownSeconds(tradeData.openTimestamp, tradeData.closeTimestamp),
            direction: tradeData.command === 1 ? 'SELL' : 'BUY',
            payout: tradeData.percentProfit,
            amount: tradeData.amount,
            profit: 0
          }
        ])
      }

      if (event === 'trade:closed') {
        const tradeData = data as CloseOrderResponse

        // Update the trade status and profit for all closed deals
        setTrades((prevTrades) => {
          return prevTrades.map((trade) => {
            // Check if this trade matches any of the closed deals
            const matchingDeal = tradeData.deals.find((deal) => deal.id === trade.id)

            if (matchingDeal) {
              return {
                ...trade,
                status: matchingDeal.profit > 0 ? 'WIN' : 'LOSS',
                closeInSeconds: 0,
                profit: matchingDeal.profit
              }
            }
            return trade
          })
        })
      }
    })

    const unsubSession = window.api.on('trade:newSession', () => {
      setTrades([])
    })

    return () => {
      unsub()
      unsubSession()
    }
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setTrades((prevTrades) =>
        prevTrades.map((trade) => ({
          ...trade,
          closeInSeconds:
            trade.status === 'PROGRESS' && trade.closeInSeconds > 0
              ? trade.closeInSeconds - 1
              : trade.closeInSeconds
        }))
      )
    }, 1000)

    return () => clearInterval(interval)
  }, [])

  // Auto-scroll to bottom when new trades are added
  useEffect(() => {
    if (tradesContainerRef.current) {
      tradesContainerRef.current.scrollTop = tradesContainerRef.current.scrollHeight
    }
  }, [trades.length])

  const getProfitColor = (profit: number): string => {
    if (profit > 0) return 'text-green-400'
    if (profit < 0) return 'text-red-500'
    return 'text-gray-500'
  }

  const getStatusBadgeClass = (status: string): string => {
    switch (status) {
      case 'WIN':
        return 'bg-green-800 text-white'
      case 'LOSS':
        return 'bg-red-800 text-white'
      case 'PROGRESS':
        return 'bg-blue-800 text-white'
      default:
        return 'bg-gray-800 text-white'
    }
  }

  return (
    <div className="w-[250px] bg-gray-800/80 p-2 h-full rounded-sm shadow-md">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-500">
          Trades: <span className="text-white !font-semibold">{trades.length}</span>
        </div>

        <div className="text-sm text-gray-500">W/L: {calculateWinLossRatio()}</div>
        <div className="text-sm text-gray-500">
          Balance: <span className="text-green-400 !font-semibold">${balance}</span>
        </div>
      </div>
      <div className="overflow-y-auto h-[574px] custom-scrollbar" ref={tradesContainerRef}>
        <div className="w-full bg-gray-800 text-white">
          {/* Sticky Header */}
          <div className="sticky top-0 bg-gray-700 z-10">
            <div className="grid grid-cols-6 gap-2 px-2 py-1">
              <div className="text-left font-bold text-xs text-gray-200 block w-10">#</div>
              <div className="text-left font-bold text-xs text-gray-200">STAT</div>
              <div className="text-center font-bold text-xs text-gray-200">DIR</div>
              <div className="text-left font-bold text-xs text-gray-200">PAY</div>
              <div className="text-left font-bold text-xs text-gray-200">AMT</div>
              <div className="text-left font-bold text-xs text-gray-200">P/L</div>
            </div>
          </div>
          {/* Trades List */}
          <div className="divide-y divide-gray-600">
            {trades.map((trade, index) => (
              <div
                key={trade.id}
                className="grid grid-cols-6 gap-2 px-2 py-2 hover:bg-gray-700/50 transition-colors"
              >
                <div className="text-xs block w-10">{index + 1}</div>
                <div className="flex justify-center items-center text-xs">
                  {trade.status === 'PROGRESS' ? (
                    `${trade.closeInSeconds}s`
                  ) : (
                    <div
                      className={`px-2 py-1 flex justify-center items-center rounded-xs max-w-[50px] text-[11px] w-full text-center ${getStatusBadgeClass(trade.status)}`}
                    >
                      <span>{trade.status}</span>
                    </div>
                  )}
                </div>
                <div className="text-xs text-center flex justify-center items-center">
                  <span className={trade.direction === 'BUY' ? 'text-green-400' : 'text-red-500'}>
                    {trade.direction}
                  </span>
                </div>
                <div className="text-xs flex justify-center items-center text-right">
                  {trade.payout}%
                </div>
                <div className="text-xs flex justify-center items-center text-right">
                  ${trade.amount}
                </div>
                <div
                  className={`text-xs flex justify-center items-center text-right ${getProfitColor(trade.profit)}`}
                >
                  ${trade.profit}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default TradesTable
