import TradesTable from './components/TradesTable'
import TradeForm from './components/TradeForm'
import ConnectionForm from './components/ConnectionForm'
import MoneyManagementStatus from './components/MoneyManagementStatus'
import LogViewer from './components/LogViewer'

function App(): React.JSX.Element {
  return (
    <main className="flex flex-col relative items-center justify-center">
      <div className="flex flex-col gap-2 justify-start w-full p-2">
        <ConnectionForm />
      </div>
      <div className="flex gap-2 justify-start w-full p-2">
        <div className="flex flex-col gap-2 flex-1">
          <LogViewer />
          <MoneyManagementStatus />
        </div>

        <div className="flex flex-col gap-2 w-[180px]">
          <TradeForm />
        </div>
        <TradesTable />
      </div>
    </main>
  )
}

export default App
