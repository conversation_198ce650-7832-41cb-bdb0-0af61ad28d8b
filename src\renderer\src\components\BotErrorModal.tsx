import { useState, useEffect } from 'react'

interface BotErrorModalProps {
  isOpen: boolean
  onClose: () => void
  error: string | null
}

const BotErrorModal: React.FC<BotErrorModalProps> = ({ isOpen, onClose, error }) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true)
    }
  }, [isOpen])

  const handleClose = (): void => {
    setIsVisible(false)
    setTimeout(onClose, 300) // Wait for animation to complete
  }

  if (!isOpen || !error) return null

  // Parse error message to extract balance information if it's an insufficient balance error
  const parseBalanceError = (
    errorMsg: string
  ): { isBalanceError: boolean; available?: string; required?: string } => {
    const balanceMatch = errorMsg.match(/Available: \$([0-9,.]+), Required: \$([0-9,.]+)/)
    if (balanceMatch) {
      return {
        isBalanceError: true,
        available: balanceMatch[1],
        required: balanceMatch[2]
      }
    }
    return { isBalanceError: false }
  }

  const balanceInfo = parseBalanceError(error)

  const getErrorIcon = (): string => {
    if (balanceInfo.isBalanceError) {
      return '💰❌'
    }
    if (error.toLowerCase().includes('connection') || error.toLowerCase().includes('socket')) {
      return '🔌❌'
    }
    if (error.toLowerCase().includes('authentication') || error.toLowerCase().includes('auth')) {
      return '🔐❌'
    }
    return '⚠️❌'
  }

  const getErrorTitle = (): string => {
    if (balanceInfo.isBalanceError) {
      return 'Insufficient Account Balance'
    }
    if (error.toLowerCase().includes('connection') || error.toLowerCase().includes('socket')) {
      return 'Connection Error'
    }
    if (error.toLowerCase().includes('authentication') || error.toLowerCase().includes('auth')) {
      return 'Authentication Error'
    }
    return 'Bot Startup Failed'
  }

  const getErrorDescription = (): string => {
    if (balanceInfo.isBalanceError) {
      return 'Your account balance is insufficient to start trading with the specified capital amount.'
    }
    if (error.toLowerCase().includes('connection') || error.toLowerCase().includes('socket')) {
      return 'Unable to connect to the trading server. Please check your internet connection.'
    }
    if (error.toLowerCase().includes('authentication') || error.toLowerCase().includes('auth')) {
      return 'Failed to authenticate with the trading platform. Please check your credentials.'
    }
    return 'The trading bot failed to start due to an unexpected error.'
  }

  const getSuggestion = (): string => {
    if (balanceInfo.isBalanceError) {
      return 'Please reduce your trade capital amount or add funds to your account.'
    }
    if (error.toLowerCase().includes('connection') || error.toLowerCase().includes('socket')) {
      return 'Please check your internet connection and try again.'
    }
    if (error.toLowerCase().includes('authentication') || error.toLowerCase().includes('auth')) {
      return 'Please verify your session credentials and restart the application.'
    }
    return 'Please check the error details and try again.'
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        className={`bg-gray-800 rounded-sm p-6 max-w-md w-full mx-4 transform transition-all duration-300 border border-red-500/30 ${
          isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
        }`}
      >
        {/* Header */}
        <div className="text-center mb-4">
          <div className="text-4xl mb-3">{getErrorIcon()}</div>
          <h2 className="text-xl font-bold text-red-400 mb-2">{getErrorTitle()}</h2>
          <p className="text-gray-300 text-sm">{getErrorDescription()}</p>
        </div>

        {/* Balance Error Details */}
        {balanceInfo.isBalanceError && (
          <div className="bg-red-900/20 border border-red-500/30 rounded p-2 mb-4">
            <div className="flex flex-col">
              <div className="flex justify-between items-center">
                <span className="text-gray-300 text-sm">Available Balance:</span>
                <span className="!font-semibold text-red-400">${balanceInfo.available}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-300 text-sm">Required Capital:</span>
                <span className="!font-semibold text-white">${balanceInfo.required}</span>
              </div>
              <div className="border-t border-red-500/30 pt-2 mt-2">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300 text-sm">Shortage:</span>
                  <span className="!font-semibold text-red-400">
                    $
                    {balanceInfo.required &&
                      balanceInfo.available &&
                      (
                        parseFloat(balanceInfo.required.replace(/,/g, '')) -
                        parseFloat(balanceInfo.available.replace(/,/g, ''))
                      ).toFixed(2)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Error Message */}
        <div className="bg-gray-700/50 p-3 rounded mb-4 border-l-4 border-red-500">
          <div className="text-xs text-gray-400 mb-1">Error Details:</div>
          <div className="text-sm text-white font-mono break-words">{error}</div>
        </div>

        {/* Suggestion */}
        <div className="bg-blue-900/20 border border-blue-500/30 rounded p-3 mb-4">
          <div className="text-xs text-blue-300 mb-1">💡 Suggestion:</div>
          <div className="text-sm text-blue-100">{getSuggestion()}</div>
        </div>

        {/* Action Button */}
        <div className="flex justify-center">
          <button
            onClick={handleClose}
            className="px-6 py-2 bg-red-600 hover:bg-red-700 text-white rounded-sm transition-colors font-medium"
          >
            Understood
          </button>
        </div>
      </div>
    </div>
  )
}

export default BotErrorModal
