import { BrowserWindow } from 'electron'
import { Logger } from '../../shared/utils/Logger'

const logger = Logger.getContextLogger('LOG_EMITTER')

export function sendToLogViewer(...args: unknown[]): void {
  const windows = BrowserWindow.getAllWindows()

  if (windows && windows.length > 0) {
    windows.forEach((window) => {
      try {
        window.webContents.send('log:event', ...args)
      } catch (error) {
        const message = getErrorMessage(error)
        logger.error(`Failed to send log event to window: ${message}`)
      }
    })
  }
}

function getErrorMessage(error: unknown): string {
  return error instanceof Error ? error.message : 'Unknown error'
}
